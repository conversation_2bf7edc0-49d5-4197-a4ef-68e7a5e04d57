# Cloudflare Workers NAT64 基准测试部署指南

## 📋 部署前准备

### 1. 环境要求
- Node.js 16+ 
- npm 或 yarn
- Cloudflare 账户
- Git (可选)

### 2. 账户准备
- 注册 [Cloudflare 账户](https://dash.cloudflare.com/sign-up)
- 获取 API Token 或使用 OAuth 登录

## 🚀 快速部署

### 方法一：自动部署脚本（推荐）

```bash
# 1. 给部署脚本执行权限
chmod +x deploy.sh

# 2. 运行自动部署
./deploy.sh
```

### 方法二：手动部署

```bash
# 1. 安装依赖
npm install

# 2. 登录 Cloudflare
npx wrangler login

# 3. 部署到 staging
npx wrangler deploy --env staging

# 4. 测试 staging 环境
curl https://workers-nat64-benchmark-staging.your-subdomain.workers.dev/benchmark

# 5. 部署到生产环境
npx wrangler deploy --env production
```

## 📁 文件说明

```
├── benchmark-worker.js     # 主要的 Worker 代码
├── wrangler.toml          # Cloudflare Workers 配置
├── package.json           # Node.js 项目配置
├── deploy.sh              # 自动部署脚本
└── README-deployment.md   # 部署说明文档
```

## ⚙️ 配置说明

### wrangler.toml 配置

```toml
name = "workers-nat64-benchmark"
main = "benchmark-worker.js"
compatibility_date = "2024-01-01"

[env.production]
name = "workers-nat64-benchmark"

[env.staging]
name = "workers-nat64-benchmark-staging"
```

**重要配置项**：
- `name`: Worker 的名称
- `main`: 入口文件
- `compatibility_date`: 兼容性日期

### 环境变量配置

如需添加环境变量，在 `wrangler.toml` 中添加：

```toml
[vars]
ENVIRONMENT = "production"
TEST_MODE = "true"
UUID = "your-uuid-here"
```

## 🧪 测试部署

### 1. 本地开发测试

```bash
# 启动本地开发服务器
npm run dev

# 访问本地测试
curl http://localhost:8787/benchmark
```

### 2. Staging 环境测试

```bash
# 部署到 staging
npm run deploy-staging

# 测试 staging 环境
curl https://workers-nat64-benchmark-staging.your-subdomain.workers.dev/benchmark
```

### 3. 生产环境测试

```bash
# 部署到生产环境
npm run deploy

# 测试生产环境
curl https://workers-nat64-benchmark.your-subdomain.workers.dev/benchmark
```

## 📊 使用基准测试

### 可用端点

| 端点 | 功能 | 示例 |
|------|------|------|
| `/` | 主页 | `curl https://your-worker.workers.dev/` |
| `/benchmark` | 基准测试 | `curl https://your-worker.workers.dev/benchmark` |
| `/compare` | 性能对比 | `curl https://your-worker.workers.dev/compare` |

### 基准测试示例

```bash
# 运行基准测试
curl https://your-worker.workers.dev/benchmark | jq

# 运行性能对比
curl https://your-worker.workers.dev/compare | jq

# 保存测试结果
curl https://your-worker.workers.dev/benchmark > benchmark-results.json
```

### 测试结果解读

```json
{
  "original_dns": {
    "totalTime": 2500,
    "averageTime": 50,
    "iterations": 50,
    "throughput": 20
  },
  "optimized_dns": {
    "totalTime": 500,
    "averageTime": 10,
    "iterations": 50,
    "throughput": 100
  }
}
```

## 🔧 管理和维护

### 查看实时日志

```bash
# 查看 Worker 实时日志
npm run tail

# 或者直接使用 wrangler
npx wrangler tail
```

### 更新部署

```bash
# 更新代码后重新部署
npm run deploy

# 或者使用脚本
./deploy.sh
```

### 删除 Worker

```bash
# 删除 staging 环境
npx wrangler delete --env staging

# 删除生产环境
npx wrangler delete --env production
```

## 📈 监控和优化

### 1. Cloudflare Analytics

- 访问 [Cloudflare Dashboard](https://dash.cloudflare.com)
- 进入 Workers & Pages
- 查看 Analytics 标签页

### 2. 性能监控指标

关键指标：
- 请求数量
- 响应时间
- 错误率
- CPU 使用时间
- 内存使用

### 3. 自动化监控

设置定时任务监控：

```bash
# 每小时运行一次基准测试
0 * * * * curl https://your-worker.workers.dev/benchmark >> /var/log/benchmark.log
```

## 🚨 故障排除

### 常见问题

1. **部署失败**
   ```bash
   # 检查 wrangler 配置
   npx wrangler whoami
   
   # 重新登录
   npx wrangler login
   ```

2. **Worker 无响应**
   ```bash
   # 查看日志
   npx wrangler tail
   
   # 检查代码语法
   node -c benchmark-worker.js
   ```

3. **性能测试异常**
   ```bash
   # 检查网络连接
   curl -v https://*******/dns-query
   
   # 检查 Worker 状态
   curl -I https://your-worker.workers.dev/
   ```

### 调试技巧

1. **启用详细日志**
   ```javascript
   console.log('Debug info:', data);
   ```

2. **使用 try-catch**
   ```javascript
   try {
     // 测试代码
   } catch (error) {
     console.error('Error:', error);
     return new Response(error.message, { status: 500 });
   }
   ```

3. **本地测试**
   ```bash
   # 本地运行测试
   npm run dev
   ```

## 🔒 安全考虑

1. **API 限制**
   - 设置适当的请求频率限制
   - 监控异常流量

2. **环境变量**
   - 不要在代码中硬编码敏感信息
   - 使用 Cloudflare 的环境变量功能

3. **访问控制**
   - 考虑添加基本的访问控制
   - 监控和记录访问日志

## 📞 支持

如果遇到问题：

1. 查看 [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
2. 检查 [Wrangler CLI 文档](https://developers.cloudflare.com/workers/wrangler/)
3. 查看项目的 GitHub Issues
4. 联系技术支持

---

**注意**: 请确保在生产环境部署前充分测试所有功能。
