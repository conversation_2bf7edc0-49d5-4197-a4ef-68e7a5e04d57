// Cloudflare Worker 基准测试
// 可以直接部署到 Cloudflare Workers 进行实际性能测试

// 简化的性能测试工具
class WorkerBenchmark {
  constructor() {
    this.results = {};
  }

  async measureTime(name, fn, iterations = 1000) {
    const start = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      await fn();
    }
    
    const end = Date.now();
    const totalTime = end - start;
    
    this.results[name] = {
      totalTime,
      averageTime: totalTime / iterations,
      iterations,
      throughput: iterations / (totalTime / 1000)
    };
    
    return this.results[name];
  }

  getResults() {
    return this.results;
  }
}

// 原版 DNS 解析（无缓存）
async function originalDNSLookup(domain) {
  const response = await fetch(`https://1.1.1.1/dns-query?name=${domain}&type=A`, {
    headers: { 'Accept': 'application/dns-json' }
  });
  return await response.json();
}

// 优化版 DNS 解析（带缓存）
const dnsCache = new Map();
const CACHE_TTL = 300000; // 5分钟

async function optimizedDNSLookup(domain) {
  const cacheKey = `dns:${domain}`;
  const cached = dnsCache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  
  const response = await fetch(`https://1.1.1.1/dns-query?name=${domain}&type=A`, {
    headers: { 'Accept': 'application/dns-json' },
    cf: { cacheTtl: 300 }
  });
  
  const data = await response.json();
  
  dnsCache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });
  
  // 限制缓存大小
  if (dnsCache.size > 100) {
    const firstKey = dnsCache.keys().next().value;
    dnsCache.delete(firstKey);
  }
  
  return data;
}

// 原版 UUID 格式化
function originalFormatUUID(bytes) {
  const hex = Array.from(bytes, b => b.toString(16).padStart(2, '0')).join('');
  return `${hex.slice(0,8)}-${hex.slice(8,12)}-${hex.slice(12,16)}-${hex.slice(16,20)}-${hex.slice(20)}`;
}

// 优化版 UUID 格式化
function optimizedFormatUUID(bytes) {
  const hex = [];
  for (let i = 0; i < bytes.length; i++) {
    hex.push(bytes[i].toString(16).padStart(2, '0'));
  }
  const hexStr = hex.join('');
  return `${hexStr.slice(0,8)}-${hexStr.slice(8,12)}-${hexStr.slice(12,16)}-${hexStr.slice(16,20)}-${hexStr.slice(20)}`;
}

// 原版数据合并
function originalCombineData(header, data) {
  const combined = new Uint8Array(header.byteLength + data.byteLength);
  combined.set(new Uint8Array(header), 0);
  combined.set(new Uint8Array(data), header.byteLength);
  return combined;
}

// 优化版数据合并（使用对象池）
class BufferPool {
  constructor(size = 10, bufferSize = 8192) {
    this.pool = [];
    for (let i = 0; i < size; i++) {
      this.pool.push(new Uint8Array(bufferSize));
    }
  }
  
  get() {
    return this.pool.pop() || new Uint8Array(8192);
  }
  
  release(buffer) {
    if (this.pool.length < 10) {
      buffer.fill(0);
      this.pool.push(buffer);
    }
  }
}

const bufferPool = new BufferPool();

function optimizedCombineData(header, data) {
  const totalLength = header.byteLength + data.byteLength;
  const buffer = bufferPool.get();
  
  if (totalLength <= buffer.length) {
    const result = buffer.subarray(0, totalLength);
    result.set(new Uint8Array(header), 0);
    result.set(new Uint8Array(data), header.byteLength);
    return result;
  } else {
    bufferPool.release(buffer);
    return originalCombineData(header, data);
  }
}

// 主要的 Worker 处理函数
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    // 基准测试端点
    if (url.pathname === '/benchmark') {
      return await runBenchmark();
    }
    
    // 实时性能对比端点
    if (url.pathname === '/compare') {
      return await runComparison();
    }
    
    // 默认响应
    return new Response(`
      <h1>Cloudflare Worker 性能测试</h1>
      <p><a href="/benchmark">运行基准测试</a></p>
      <p><a href="/compare">运行性能对比</a></p>
    `, {
      headers: { 'Content-Type': 'text/html' }
    });
  }
};

async function runBenchmark() {
  const benchmark = new WorkerBenchmark();
  
  try {
    // 测试 DNS 解析性能
    await benchmark.measureTime('original_dns', async () => {
      await originalDNSLookup('example.com');
    }, 50);
    
    await benchmark.measureTime('optimized_dns', async () => {
      await optimizedDNSLookup('example.com');
    }, 50);
    
    // 测试 UUID 格式化性能
    const testBytes = new Uint8Array(16).fill(0).map((_, i) => i);
    
    await benchmark.measureTime('original_uuid', () => {
      originalFormatUUID(testBytes);
    }, 10000);
    
    await benchmark.measureTime('optimized_uuid', () => {
      optimizedFormatUUID(testBytes);
    }, 10000);
    
    // 测试数据合并性能
    const header = new Uint8Array([1, 2, 3, 4]);
    const data = new Uint8Array(1000).fill(5);
    
    await benchmark.measureTime('original_combine', () => {
      originalCombineData(header, data);
    }, 1000);
    
    await benchmark.measureTime('optimized_combine', () => {
      optimizedCombineData(header, data);
    }, 1000);
    
    const results = benchmark.getResults();
    
    return new Response(JSON.stringify(results, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    return new Response(`测试错误: ${error.message}`, { status: 500 });
  }
}

async function runComparison() {
  const benchmark = new WorkerBenchmark();
  
  try {
    // 并发测试 DNS 解析
    const domains = ['google.com', 'cloudflare.com', 'github.com'];
    
    // 原版并发测试
    const originalStart = Date.now();
    await Promise.all(domains.map(domain => originalDNSLookup(domain)));
    const originalTime = Date.now() - originalStart;
    
    // 优化版并发测试
    const optimizedStart = Date.now();
    await Promise.all(domains.map(domain => optimizedDNSLookup(domain)));
    const optimizedTime = Date.now() - optimizedStart;
    
    // 第二轮测试（测试缓存效果）
    const cachedStart = Date.now();
    await Promise.all(domains.map(domain => optimizedDNSLookup(domain)));
    const cachedTime = Date.now() - cachedStart;
    
    const comparison = {
      dns_comparison: {
        original_time: originalTime,
        optimized_time: optimizedTime,
        cached_time: cachedTime,
        improvement: ((originalTime - optimizedTime) / originalTime * 100).toFixed(2) + '%',
        cache_improvement: ((optimizedTime - cachedTime) / optimizedTime * 100).toFixed(2) + '%'
      },
      cache_stats: {
        cache_size: dnsCache.size,
        cache_hit_ratio: cachedTime < optimizedTime ? 'High' : 'Low'
      }
    };
    
    return new Response(JSON.stringify(comparison, null, 2), {
      headers: { 'Content-Type': 'application/json' }
    });
    
  } catch (error) {
    return new Response(`对比测试错误: ${error.message}`, { status: 500 });
  }
}
