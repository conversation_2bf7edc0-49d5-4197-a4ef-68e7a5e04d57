# Cloudflare Workers 配置文件
name = "workers-nat64-benchmark"
main = "benchmark-worker.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Worker 配置
[env.production]
name = "workers-nat64-benchmark"
route = ""
zone_id = ""

[env.staging]
name = "workers-nat64-benchmark-staging"

# 环境变量
[vars]
ENVIRONMENT = "production"
TEST_MODE = "true"

# 资源限制
[limits]
cpu_ms = 50000

# 触发器配置
[[triggers]]
crons = ["0 */6 * * *"]  # 每6小时运行一次自动基准测试

# KV 命名空间（可选，用于存储测试结果）
# [[kv_namespaces]]
# binding = "BENCHMARK_RESULTS"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"
