// Cloudflare Worker 性能测试对比脚本
// 用于测试原版 vs 优化版的性能差异

class PerformanceTestSuite {
  constructor() {
    this.results = {
      original: {},
      optimized: {}
    };
    this.testConfig = {
      iterations: 1000,
      concurrency: 10,
      testDomains: [
        'google.com',
        'cloudflare.com', 
        'github.com',
        'stackoverflow.com',
        'example.com'
      ],
      testIPs: [
        '*******',
        '*******',
        '**************',
        '*******'
      ]
    };
  }

  // 测试 DNS 解析性能
  async testDNSPerformance(version, dnsFunction) {
    console.log(`开始测试 ${version} 版本的 DNS 解析性能...`);
    
    const startTime = performance.now();
    const promises = [];
    
    for (let i = 0; i < this.testConfig.iterations; i++) {
      const domain = this.testConfig.testDomains[i % this.testConfig.testDomains.length];
      promises.push(this.measureDNSQuery(dnsFunction, domain));
      
      // 控制并发数
      if (promises.length >= this.testConfig.concurrency) {
        await Promise.allSettled(promises.splice(0, this.testConfig.concurrency));
      }
    }
    
    // 处理剩余的请求
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    this.results[version].dns = {
      totalTime,
      averageTime: totalTime / this.testConfig.iterations,
      throughput: this.testConfig.iterations / (totalTime / 1000)
    };
    
    console.log(`${version} DNS 测试完成: ${totalTime.toFixed(2)}ms`);
  }

  async measureDNSQuery(dnsFunction, domain) {
    const start = performance.now();
    try {
      await dnsFunction(domain);
      return performance.now() - start;
    } catch (error) {
      console.warn(`DNS 查询失败 ${domain}:`, error.message);
      return performance.now() - start;
    }
  }

  // 测试内存使用情况
  async testMemoryUsage(version, testFunction) {
    console.log(`开始测试 ${version} 版本的内存使用...`);
    
    // 强制垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }
    
    const initialMemory = this.getMemoryUsage();
    
    // 执行大量操作
    for (let i = 0; i < 1000; i++) {
      await testFunction();
    }
    
    const finalMemory = this.getMemoryUsage();
    
    this.results[version].memory = {
      initial: initialMemory,
      final: finalMemory,
      difference: finalMemory - initialMemory,
      growth: ((finalMemory - initialMemory) / initialMemory * 100).toFixed(2)
    };
    
    console.log(`${version} 内存测试完成`);
  }

  getMemoryUsage() {
    // 在 Cloudflare Workers 环境中，这个方法可能不可用
    // 这里提供一个模拟实现
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    return 0; // 在 Workers 环境中返回 0
  }

  // 测试连接建立性能
  async testConnectionPerformance(version, connectFunction) {
    console.log(`开始测试 ${version} 版本的连接性能...`);
    
    const results = [];
    
    for (let i = 0; i < 100; i++) {
      const ip = this.testConfig.testIPs[i % this.testConfig.testIPs.length];
      const port = 80;
      
      const start = performance.now();
      try {
        await connectFunction(ip, port);
        results.push(performance.now() - start);
      } catch (error) {
        console.warn(`连接失败 ${ip}:${port}:`, error.message);
        results.push(performance.now() - start);
      }
    }
    
    this.results[version].connection = {
      average: results.reduce((a, b) => a + b, 0) / results.length,
      min: Math.min(...results),
      max: Math.max(...results),
      median: this.calculateMedian(results)
    };
    
    console.log(`${version} 连接测试完成`);
  }

  calculateMedian(arr) {
    const sorted = arr.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    
    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    }
    return sorted[middle];
  }

  // 测试协议解析性能
  async testProtocolParsingPerformance(version, parseFunction) {
    console.log(`开始测试 ${version} 版本的协议解析性能...`);
    
    // 创建测试数据
    const testData = this.generateTestVLESSData();
    const iterations = 10000;
    
    const start = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      try {
        parseFunction(testData, 'test-uuid-1234-5678-9abc-def012345678');
      } catch (error) {
        // 忽略解析错误，专注于性能测试
      }
    }
    
    const end = performance.now();
    const totalTime = end - start;
    
    this.results[version].parsing = {
      totalTime,
      averageTime: totalTime / iterations,
      throughput: iterations / (totalTime / 1000)
    };
    
    console.log(`${version} 协议解析测试完成: ${totalTime.toFixed(2)}ms`);
  }

  generateTestVLESSData() {
    // 生成模拟的 VLESS 协议数据
    const buffer = new ArrayBuffer(100);
    const view = new DataView(buffer);
    
    // 版本
    view.setUint8(0, 0);
    
    // UUID (16 bytes)
    for (let i = 1; i <= 16; i++) {
      view.setUint8(i, Math.floor(Math.random() * 256));
    }
    
    // 附加信息长度
    view.setUint8(17, 0);
    
    // 命令 (TCP)
    view.setUint8(18, 1);
    
    // 端口
    view.setUint16(19, 80);
    
    // 地址类型 (IPv4)
    view.setUint8(21, 1);
    
    // IPv4 地址
    view.setUint8(22, 8);
    view.setUint8(23, 8);
    view.setUint8(24, 8);
    view.setUint8(25, 8);
    
    return buffer;
  }

  // 生成性能报告
  generateReport() {
    console.log('\n=== 性能测试报告 ===\n');
    
    // DNS 性能对比
    if (this.results.original.dns && this.results.optimized.dns) {
      console.log('📊 DNS 解析性能对比:');
      console.log(`原版平均时间: ${this.results.original.dns.averageTime.toFixed(2)}ms`);
      console.log(`优化版平均时间: ${this.results.optimized.dns.averageTime.toFixed(2)}ms`);
      const dnsImprovement = ((this.results.original.dns.averageTime - this.results.optimized.dns.averageTime) / this.results.original.dns.averageTime * 100).toFixed(2);
      console.log(`性能提升: ${dnsImprovement}%\n`);
    }
    
    // 内存使用对比
    if (this.results.original.memory && this.results.optimized.memory) {
      console.log('💾 内存使用对比:');
      console.log(`原版内存增长: ${this.results.original.memory.growth}%`);
      console.log(`优化版内存增长: ${this.results.optimized.memory.growth}%`);
      const memoryImprovement = (this.results.original.memory.growth - this.results.optimized.memory.growth).toFixed(2);
      console.log(`内存优化: ${memoryImprovement}%\n`);
    }
    
    // 连接性能对比
    if (this.results.original.connection && this.results.optimized.connection) {
      console.log('🔗 连接性能对比:');
      console.log(`原版平均连接时间: ${this.results.original.connection.average.toFixed(2)}ms`);
      console.log(`优化版平均连接时间: ${this.results.optimized.connection.average.toFixed(2)}ms`);
      const connImprovement = ((this.results.original.connection.average - this.results.optimized.connection.average) / this.results.original.connection.average * 100).toFixed(2);
      console.log(`连接性能提升: ${connImprovement}%\n`);
    }
    
    // 协议解析性能对比
    if (this.results.original.parsing && this.results.optimized.parsing) {
      console.log('⚡ 协议解析性能对比:');
      console.log(`原版吞吐量: ${this.results.original.parsing.throughput.toFixed(0)} ops/sec`);
      console.log(`优化版吞吐量: ${this.results.optimized.parsing.throughput.toFixed(0)} ops/sec`);
      const parseImprovement = ((this.results.optimized.parsing.throughput - this.results.original.parsing.throughput) / this.results.original.parsing.throughput * 100).toFixed(2);
      console.log(`解析性能提升: ${parseImprovement}%\n`);
    }
    
    return this.results;
  }
}

// 导出测试套件
export { PerformanceTestSuite };
