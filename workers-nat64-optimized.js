let userID = '';

import { connect } from 'cloudflare:sockets';

// WebSocket 状态常量
const WS_READY_STATE_OPEN = 1;
const WS_READY_STATE_CLOSING = 2;

// 性能优化：DNS 缓存
const DNS_CACHE = new Map();
const DNS_CACHE_TTL = 300000; // 5分钟缓存

// 性能优化：连接池
const CONNECTION_POOL = new Map();
const MAX_POOL_SIZE = 100;
const CONNECTION_TIMEOUT = 30000; // 30秒超时

// 性能优化：对象池
class BufferPool {
  constructor(size = 50, bufferSize = 8192) {
    this.pool = [];
    this.size = size;
    this.bufferSize = bufferSize;
    this.init();
  }
  
  init() {
    for (let i = 0; i < this.size; i++) {
      this.pool.push(new Uint8Array(this.bufferSize));
    }
  }
  
  get() {
    return this.pool.pop() || new Uint8Array(this.bufferSize);
  }
  
  release(buffer) {
    if (this.pool.length < this.size && buffer.length === this.bufferSize) {
      buffer.fill(0); // 清零重用
      this.pool.push(buffer);
    }
  }
}

const bufferPool = new BufferPool();

// 性能优化：预编译正则表达式
const IPV4_REGEX = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;

export default {
  async fetch(request, env, ctx) {
    try {
      userID = env.UUID || userID;
      
      const upgradeHeader = request.headers.get('Upgrade');
      if (!upgradeHeader || upgradeHeader.toLowerCase() !== 'websocket') {
        return handleHttpRequest(request);
      }
      
      return await handleVLESSWebSocket(request);
    } catch (err) {
      console.error('Worker error:', err);
      return new Response(err.toString(), { status: 500 });
    }
  },
};

// 性能优化：分离HTTP请求处理
function handleHttpRequest(request) {
  const url = new URL(request.url);
  
  if (url.pathname === '/') {
    return new Response('VLESS Proxy Server', { status: 200 });
  }
  
  if (url.pathname === `/${userID}`) {
    const host = request.headers.get('Host');
    const vlessConfig = `vless://${userID}@${host}:443?encryption=none&security=tls&sni=${host}&type=ws&host=${host}&path=/#${host}`;
    return new Response(vlessConfig, {
      status: 200,
      headers: { 'Content-Type': 'text/plain;charset=utf-8' },
    });
  }
  
  return new Response('Not Found', { status: 404 });
}

// 性能优化：DNS缓存机制
async function getCachedIPv6Address(domain) {
  const cacheKey = `dns:${domain}`;
  const cached = DNS_CACHE.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < DNS_CACHE_TTL) {
    return cached.address;
  }
  
  try {
    const dnsQuery = await fetch(`https://*******/dns-query?name=${domain}&type=A`, {
      headers: { 'Accept': 'application/dns-json' },
      cf: { cacheTtl: 300 } // Cloudflare 边缘缓存
    });
    
    const dnsResult = await dnsQuery.json();
    if (dnsResult.Answer && dnsResult.Answer.length > 0) {
      const aRecord = dnsResult.Answer.find(record => record.type === 1);
      if (aRecord) {
        const ipv6Address = convertToNAT64IPv6(aRecord.data);
        
        // 缓存结果
        DNS_CACHE.set(cacheKey, {
          address: ipv6Address,
          timestamp: Date.now()
        });
        
        // 性能优化：限制缓存大小
        if (DNS_CACHE.size > 1000) {
          const firstKey = DNS_CACHE.keys().next().value;
          DNS_CACHE.delete(firstKey);
        }
        
        return ipv6Address;
      }
    }
    throw new Error('无法解析域名的IPv4地址');
  } catch (err) {
    throw new Error(`DNS解析失败: ${err.message}`);
  }
}

// 性能优化：连接池管理
async function getPooledConnection(hostname, port) {
  const poolKey = `${hostname}:${port}`;
  const pooledConnections = CONNECTION_POOL.get(poolKey) || [];
  
  // 尝试复用现有连接
  while (pooledConnections.length > 0) {
    const conn = pooledConnections.pop();
    if (conn && !conn.closed) {
      return conn;
    }
  }
  
  // 创建新连接
  const tcpSocket = await connect({
    hostname,
    port,
    timeout: CONNECTION_TIMEOUT
  });
  
  return tcpSocket;
}

// 性能优化：连接回收
function releaseConnection(hostname, port, connection) {
  if (!connection || connection.closed) return;
  
  const poolKey = `${hostname}:${port}`;
  const pooledConnections = CONNECTION_POOL.get(poolKey) || [];
  
  if (pooledConnections.length < MAX_POOL_SIZE) {
    pooledConnections.push(connection);
    CONNECTION_POOL.set(poolKey, pooledConnections);
  } else {
    connection.close();
  }
}

function convertToNAT64IPv6(ipv4Address) {
  const parts = ipv4Address.split('.');
  if (parts.length !== 4) {
    throw new Error('无效的IPv4地址');
  }
  
  const hex = parts.map(part => {
    const num = parseInt(part, 10);
    if (num < 0 || num > 255) {
      throw new Error('无效的IPv4地址段');
    }
    return num.toString(16).padStart(2, '0');
  });
  
  return `[2001:67c:2960:6464::${hex[0]}${hex[1]}:${hex[2]}${hex[3]}]`;
}

// 处理 VLESS WebSocket 连接
async function handleVLESSWebSocket(request) {
  const wsPair = new WebSocketPair();
  const [clientWS, serverWS] = Object.values(wsPair);

  serverWS.accept();

  const earlyDataHeader = request.headers.get('sec-websocket-protocol') || '';
  const wsReadable = createWebSocketReadableStream(serverWS, earlyDataHeader);
  let remoteSocket = null;
  let udpStreamWrite = null;
  let isDns = false;
  
  // 性能优化：保持writer引用
  let remoteWriter = null;
  
  wsReadable.pipeTo(new WritableStream({
    async write(chunk) {
      if (isDns && udpStreamWrite) {
        return udpStreamWrite(chunk);
      }
      
      if (remoteSocket && remoteWriter) {
        await remoteWriter.write(chunk);
        return;
      }

      const result = parseVLESSHeader(chunk, userID);
      if (result.hasError) {
        throw new Error(result.message);
      }

      const vlessRespHeader = new Uint8Array([result.vlessVersion[0], 0]);
      const rawClientData = chunk.slice(result.rawDataIndex);
      
      if (result.isUDP) {
        if (result.portRemote === 53) {
          isDns = true;
          const { write } = await handleUDPOutBound(serverWS, vlessRespHeader);
          udpStreamWrite = write;
          udpStreamWrite(rawClientData);
          return;
        } else {
          throw new Error('UDP代理仅支持DNS(端口53)');
        }
      }

      try {
        // 性能优化：使用连接池
        const tcpSocket = await getPooledConnection(result.addressRemote, result.portRemote);
        remoteSocket = tcpSocket;
        remoteWriter = tcpSocket.writable.getWriter();
        await remoteWriter.write(rawClientData);
        
        pipeRemoteToWebSocket(tcpSocket, serverWS, vlessRespHeader, async () => {
          await retryWithNAT64(result, rawClientData, serverWS, vlessRespHeader);
        });
      } catch (err) {
        console.error('连接失败:', err);
        await retryWithNAT64(result, rawClientData, serverWS, vlessRespHeader);
      }
    },
    close() {
      if (remoteWriter) {
        remoteWriter.releaseLock();
      }
      if (remoteSocket) {
        closeSocket(remoteSocket);
      }
    }
  })).catch(err => {
    console.error('WebSocket 错误:', err);
    if (remoteWriter) {
      remoteWriter.releaseLock();
    }
    closeSocket(remoteSocket);
    serverWS.close(1011, '内部错误');
  });

  return new Response(null, {
    status: 101,
    webSocket: clientWS,
  });
}

// 性能优化：NAT64重试机制
async function retryWithNAT64(result, rawClientData, serverWS, vlessRespHeader) {
  try {
    let proxyIP;

    if (IPV4_REGEX.test(result.addressRemote)) {
      proxyIP = convertToNAT64IPv6(result.addressRemote);
      console.log(`直接访问IPv4地址，转换为NAT64 IPv6: ${proxyIP}`);
    } else {
      proxyIP = await getCachedIPv6Address(result.addressRemote);
      console.log(`域名解析后转换为NAT64 IPv6: ${proxyIP}`);
    }

    console.log(`尝试通过NAT64 IPv6地址 ${proxyIP} 连接...`);
    const tcpSocket = await getPooledConnection(proxyIP, result.portRemote);
    const writer = tcpSocket.writable.getWriter();
    await writer.write(rawClientData);
    writer.releaseLock();

    tcpSocket.closed.catch(error => {
      console.error('NAT64 IPv6连接关闭错误:', error);
    }).finally(() => {
      if (serverWS.readyState === WS_READY_STATE_OPEN) {
        serverWS.close(1000, '连接已关闭');
      }
    });

    pipeRemoteToWebSocket(tcpSocket, serverWS, vlessRespHeader, null);
  } catch (err) {
    console.error('NAT64 IPv6连接失败:', err);
    serverWS.close(1011, 'NAT64 IPv6连接失败: ' + err.message);
  }
}

// 性能优化：创建 WebSocket 可读流
function createWebSocketReadableStream(ws, earlyDataHeader) {
  return new ReadableStream({
    start(controller) {
      ws.addEventListener('message', event => {
        controller.enqueue(event.data);
      });

      ws.addEventListener('close', () => {
        controller.close();
      });

      ws.addEventListener('error', err => {
        controller.error(err);
      });

      // 性能优化：早期数据处理
      if (earlyDataHeader) {
        try {
          const decoded = atob(earlyDataHeader.replace(/-/g, '+').replace(/_/g, '/'));
          const data = Uint8Array.from(decoded, c => c.charCodeAt(0));
          controller.enqueue(data.buffer);
        } catch (e) {
          console.warn('早期数据解析失败:', e);
        }
      }
    }
  });
}

// 性能优化：解析 VLESS 协议头
function parseVLESSHeader(buffer, userID) {
  if (buffer.byteLength < 24) {
    return { hasError: true, message: '无效的头部长度' };
  }

  const view = new DataView(buffer);
  const version = new Uint8Array(buffer.slice(0, 1));

  // 性能优化：UUID验证
  const uuid = formatUUID(new Uint8Array(buffer.slice(1, 17)));
  if (uuid !== userID) {
    return { hasError: true, message: '无效的用户' };
  }

  const optionsLength = view.getUint8(17);
  const command = view.getUint8(18 + optionsLength);

  let isUDP = false;
  if (command === 1) {
    // TCP
  } else if (command === 2) {
    // UDP
    isUDP = true;
  } else {
    return { hasError: true, message: '不支持的命令，仅支持TCP(01)和UDP(02)' };
  }

  let offset = 19 + optionsLength;
  const port = view.getUint16(offset);
  offset += 2;

  const addressType = view.getUint8(offset++);
  let address = '';

  switch (addressType) {
    case 1: // IPv4
      address = Array.from(new Uint8Array(buffer.slice(offset, offset + 4))).join('.');
      offset += 4;
      break;

    case 2: // 域名
      const domainLength = view.getUint8(offset++);
      address = new TextDecoder().decode(buffer.slice(offset, offset + domainLength));
      offset += domainLength;
      break;

    case 3: // IPv6
      const ipv6 = [];
      for (let i = 0; i < 8; i++) {
        ipv6.push(view.getUint16(offset).toString(16).padStart(4, '0'));
        offset += 2;
      }
      address = ipv6.join(':').replace(/(^|:)0+(\w)/g, '$1$2');
      break;

    default:
      return { hasError: true, message: '不支持的地址类型' };
  }

  return {
    hasError: false,
    addressRemote: address,
    portRemote: port,
    rawDataIndex: offset,
    vlessVersion: version,
    isUDP
  };
}

// 性能优化：将远程套接字数据转发到 WebSocket
function pipeRemoteToWebSocket(remoteSocket, ws, vlessHeader, retry = null) {
  let headerSent = false;
  let hasIncomingData = false;

  // 性能优化：使用对象池的缓冲区
  const buffer = bufferPool.get();

  remoteSocket.readable.pipeTo(new WritableStream({
    write(chunk) {
      hasIncomingData = true;
      if (ws.readyState === WS_READY_STATE_OPEN) {
        if (!headerSent) {
          // 性能优化：减少内存分配
          const totalLength = vlessHeader.byteLength + chunk.byteLength;
          const combined = totalLength <= buffer.length ?
            buffer.subarray(0, totalLength) :
            new Uint8Array(totalLength);

          combined.set(new Uint8Array(vlessHeader), 0);
          combined.set(new Uint8Array(chunk), vlessHeader.byteLength);
          ws.send(combined.buffer.slice(0, totalLength));
          headerSent = true;
        } else {
          ws.send(chunk);
        }
      }
    },
    close() {
      bufferPool.release(buffer);
      if (!hasIncomingData && retry) {
        retry();
        return;
      }
      if (ws.readyState === WS_READY_STATE_OPEN) {
        ws.close(1000, '正常关闭');
      }
    },
    abort() {
      bufferPool.release(buffer);
      closeSocket(remoteSocket);
    }
  })).catch(err => {
    console.error('数据转发错误:', err);
    bufferPool.release(buffer);
    closeSocket(remoteSocket);
    if (ws.readyState === WS_READY_STATE_OPEN) {
      ws.close(1011, '数据传输错误');
    }
  });
}

// 安全关闭套接字
function closeSocket(socket) {
  if (socket) {
    try {
      socket.close();
    } catch (e) {
      console.warn('套接字关闭错误:', e);
    }
  }
}

// 性能优化：格式化 UUID
function formatUUID(bytes) {
  // 使用更高效的字符串拼接
  const hex = [];
  for (let i = 0; i < bytes.length; i++) {
    hex.push(bytes[i].toString(16).padStart(2, '0'));
  }
  const hexStr = hex.join('');
  return `${hexStr.slice(0,8)}-${hexStr.slice(8,12)}-${hexStr.slice(12,16)}-${hexStr.slice(16,20)}-${hexStr.slice(20)}`;
}

// 性能优化：处理UDP DNS请求
async function handleUDPOutBound(webSocket, vlessResponseHeader) {
  let isVlessHeaderSent = false;

  const transformStream = new TransformStream({
    transform(chunk, controller) {
      // 性能优化：批量处理UDP数据包
      const packets = [];
      for (let index = 0; index < chunk.byteLength;) {
        if (index + 2 > chunk.byteLength) break;

        const lengthBuffer = chunk.slice(index, index + 2);
        const udpPacketLength = new DataView(lengthBuffer).getUint16(0);

        if (index + 2 + udpPacketLength > chunk.byteLength) break;

        const udpData = new Uint8Array(
          chunk.slice(index + 2, index + 2 + udpPacketLength)
        );
        packets.push(udpData);
        index = index + 2 + udpPacketLength;
      }

      // 批量处理所有数据包
      packets.forEach(packet => controller.enqueue(packet));
    }
  });

  // 性能优化：DNS查询处理
  transformStream.readable.pipeTo(new WritableStream({
    async write(chunk) {
      try {
        const resp = await fetch('https://*******/dns-query', {
          method: 'POST',
          headers: {
            'content-type': 'application/dns-message',
          },
          body: chunk,
          cf: { cacheTtl: 300 } // 边缘缓存
        });

        const dnsQueryResult = await resp.arrayBuffer();
        const udpSize = dnsQueryResult.byteLength;
        const udpSizeBuffer = new Uint8Array([(udpSize >> 8) & 0xff, udpSize & 0xff]);

        if (webSocket.readyState === WS_READY_STATE_OPEN) {
          console.log(`DNS查询成功，DNS消息长度为 ${udpSize}`);

          // 性能优化：减少Blob创建
          const responseData = new Uint8Array(
            (isVlessHeaderSent ? 0 : vlessResponseHeader.length) + 2 + udpSize
          );
          let offset = 0;

          if (!isVlessHeaderSent) {
            responseData.set(vlessResponseHeader, offset);
            offset += vlessResponseHeader.length;
            isVlessHeaderSent = true;
          }

          responseData.set(udpSizeBuffer, offset);
          offset += 2;
          responseData.set(new Uint8Array(dnsQueryResult), offset);

          webSocket.send(responseData.buffer);
        }
      } catch (error) {
        console.error('DNS查询失败:', error);
      }
    }
  })).catch((error) => {
    console.error('DNS UDP处理错误:', error);
  });

  const writer = transformStream.writable.getWriter();

  return {
    write(chunk) {
      return writer.write(chunk);
    }
  };
}
