#!/bin/bash

# Cloudflare Workers 部署脚本
# 用于部署 NAT64 性能基准测试

set -e

echo "🚀 开始部署 Cloudflare Workers NAT64 基准测试..."

# 检查必要的工具
check_requirements() {
    echo "📋 检查部署要求..."
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo "❌ npm 未安装，请先安装 npm"
        exit 1
    fi
    
    echo "✅ Node.js 和 npm 已安装"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装依赖..."
    
    if [ ! -f "package.json" ]; then
        echo "❌ package.json 文件不存在"
        exit 1
    fi
    
    npm install
    echo "✅ 依赖安装完成"
}

# 检查 Wrangler 配置
check_wrangler() {
    echo "🔧 检查 Wrangler 配置..."
    
    if [ ! -f "wrangler.toml" ]; then
        echo "❌ wrangler.toml 文件不存在"
        exit 1
    fi
    
    # 检查是否已登录 Cloudflare
    if ! npx wrangler whoami &> /dev/null; then
        echo "🔑 需要登录 Cloudflare 账户..."
        npx wrangler login
    fi
    
    echo "✅ Wrangler 配置检查完成"
}

# 验证代码
validate_code() {
    echo "🔍 验证代码..."
    
    if [ ! -f "benchmark-worker.js" ]; then
        echo "❌ benchmark-worker.js 文件不存在"
        exit 1
    fi
    
    # 简单的语法检查
    node -c benchmark-worker.js
    echo "✅ 代码验证通过"
}

# 部署到 staging 环境
deploy_staging() {
    echo "🧪 部署到 staging 环境..."
    npx wrangler deploy --env staging
    echo "✅ Staging 部署完成"
    
    # 获取 staging URL
    STAGING_URL=$(npx wrangler subdomain get 2>/dev/null || echo "your-subdomain")
    echo "🌐 Staging URL: https://workers-nat64-benchmark-staging.${STAGING_URL}.workers.dev"
}

# 测试 staging 环境
test_staging() {
    echo "🧪 测试 staging 环境..."
    
    STAGING_URL=$(npx wrangler subdomain get 2>/dev/null || echo "your-subdomain")
    STAGING_ENDPOINT="https://workers-nat64-benchmark-staging.${STAGING_URL}.workers.dev"
    
    echo "测试基本响应..."
    if curl -f -s "${STAGING_ENDPOINT}" > /dev/null; then
        echo "✅ 基本响应测试通过"
    else
        echo "❌ 基本响应测试失败"
        exit 1
    fi
    
    echo "测试基准测试端点..."
    if curl -f -s "${STAGING_ENDPOINT}/benchmark" > /dev/null; then
        echo "✅ 基准测试端点正常"
    else
        echo "❌ 基准测试端点异常"
        exit 1
    fi
    
    echo "测试性能对比端点..."
    if curl -f -s "${STAGING_ENDPOINT}/compare" > /dev/null; then
        echo "✅ 性能对比端点正常"
    else
        echo "❌ 性能对比端点异常"
        exit 1
    fi
}

# 部署到生产环境
deploy_production() {
    echo "🚀 部署到生产环境..."
    
    read -p "确认要部署到生产环境吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        npx wrangler deploy --env production
        echo "✅ 生产环境部署完成"
        
        # 获取生产 URL
        PROD_URL=$(npx wrangler subdomain get 2>/dev/null || echo "your-subdomain")
        echo "🌐 生产 URL: https://workers-nat64-benchmark.${PROD_URL}.workers.dev"
    else
        echo "❌ 取消生产环境部署"
    fi
}

# 显示部署后的使用说明
show_usage() {
    echo ""
    echo "🎉 部署完成！"
    echo ""
    echo "📖 使用说明："
    echo ""
    
    SUBDOMAIN=$(npx wrangler subdomain get 2>/dev/null || echo "your-subdomain")
    BASE_URL="https://workers-nat64-benchmark.${SUBDOMAIN}.workers.dev"
    
    echo "🌐 基准测试 Worker URL:"
    echo "   ${BASE_URL}"
    echo ""
    echo "📊 可用的测试端点:"
    echo "   GET  ${BASE_URL}           - 主页"
    echo "   GET  ${BASE_URL}/benchmark - 运行基准测试"
    echo "   GET  ${BASE_URL}/compare   - 运行性能对比"
    echo ""
    echo "💻 命令行测试:"
    echo "   curl ${BASE_URL}/benchmark | jq"
    echo "   curl ${BASE_URL}/compare | jq"
    echo ""
    echo "🔧 管理命令:"
    echo "   npm run dev        - 本地开发"
    echo "   npm run tail       - 查看实时日志"
    echo "   npm run benchmark  - 快速基准测试"
    echo "   npm run compare    - 快速性能对比"
    echo ""
    echo "📈 监控建议:"
    echo "   - 设置 Cloudflare Analytics 监控"
    echo "   - 定期运行基准测试检查性能"
    echo "   - 监控 Worker 的 CPU 和内存使用"
}

# 主函数
main() {
    echo "🎯 Cloudflare Workers NAT64 基准测试部署脚本"
    echo "================================================"
    
    check_requirements
    install_dependencies
    check_wrangler
    validate_code
    
    # 部署流程
    deploy_staging
    test_staging
    
    echo ""
    echo "✅ Staging 环境测试通过！"
    echo ""
    
    deploy_production
    show_usage
    
    echo ""
    echo "🎉 部署流程完成！"
}

# 错误处理
trap 'echo "❌ 部署过程中发生错误，请检查上面的错误信息"; exit 1' ERR

# 运行主函数
main "$@"
